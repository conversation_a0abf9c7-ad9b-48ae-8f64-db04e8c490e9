{"version": 3, "file": "dist_client_dev_noop-turbopack-hmr_js-turbo-experimental.runtime.dev.js", "mappings": ";;uEAEO,SAASA,UAAW,C,iGAAXA,C,oCAAAA,O", "sources": ["webpack://next/./src/client/dev/noop-turbopack-hmr.ts"], "sourcesContent": ["// The Turbopack HMR client can't be properly omitted at the moment (WEB-1589),\n// so instead we remap its import to this file in webpack builds.\nexport function connect() {}\n"], "names": ["connect"], "sourceRoot": "", "ignoreList": [0]}