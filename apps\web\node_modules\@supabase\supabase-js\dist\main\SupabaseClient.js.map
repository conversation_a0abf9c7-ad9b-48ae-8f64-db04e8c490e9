{"version": 3, "file": "SupabaseClient.js", "sourceRoot": "", "sources": ["../../src/SupabaseClient.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,yDAAwD;AAExD,yDAI+B;AAC/B,uDAK8B;AAC9B,qDAA6E;AAC7E,+CAKwB;AACxB,uCAA2C;AAC3C,2CAAyE;AACzE,iEAA6D;AAG7D;;;;GAIG;AACH,MAAqB,cAAc;IA2BjC;;;;;;;;;;;OAWG;IACH,YACY,WAAmB,EACnB,WAAmB,EAC7B,OAA2C;;QAFjC,gBAAW,GAAX,WAAW,CAAQ;QACnB,gBAAW,GAAX,WAAW,CAAQ;QAG7B,IAAI,CAAC,WAAW;YAAE,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAA;QAC7D,IAAI,CAAC,WAAW;YAAE,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAA;QAE7D,MAAM,YAAY,GAAG,IAAA,6BAAmB,EAAC,WAAW,CAAC,CAAA;QACrD,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,CAAA;QAErC,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC,CAAA;QAClD,IAAI,CAAC,WAAW,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QAC3E,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA;QAC1C,IAAI,CAAC,UAAU,GAAG,IAAI,GAAG,CAAC,YAAY,EAAE,OAAO,CAAC,CAAA;QAChD,IAAI,CAAC,YAAY,GAAG,IAAI,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC,CAAA;QAEpD,mEAAmE;QACnE,MAAM,iBAAiB,GAAG,MAAM,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,CAAA;QAC3E,MAAM,QAAQ,GAAG;YACf,EAAE,EAAE,8BAAkB;YACtB,QAAQ,EAAE,oCAAwB;YAClC,IAAI,kCAAO,gCAAoB,KAAE,UAAU,EAAE,iBAAiB,GAAE;YAChE,MAAM,EAAE,kCAAsB;SAC/B,CAAA;QAED,MAAM,QAAQ,GAAG,IAAA,8BAAoB,EAAC,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,EAAE,EAAE,QAAQ,CAAC,CAAA;QAE9D,IAAI,CAAC,UAAU,GAAG,MAAA,QAAQ,CAAC,IAAI,CAAC,UAAU,mCAAI,EAAE,CAAA;QAChD,IAAI,CAAC,OAAO,GAAG,MAAA,QAAQ,CAAC,MAAM,CAAC,OAAO,mCAAI,EAAE,CAAA;QAE5C,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE;YACzB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,uBAAuB,CACtC,MAAA,QAAQ,CAAC,IAAI,mCAAI,EAAE,EACnB,IAAI,CAAC,OAAO,EACZ,QAAQ,CAAC,MAAM,CAAC,KAAK,CACtB,CAAA;SACF;aAAM;YACL,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAA;YAEvC,IAAI,CAAC,IAAI,GAAG,IAAI,KAAK,CAAqB,EAAS,EAAE;gBACnD,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE;oBACf,MAAM,IAAI,KAAK,CACb,6GAA6G,MAAM,CACjH,IAAI,CACL,kBAAkB,CACpB,CAAA;gBACH,CAAC;aACF,CAAC,CAAA;SACH;QAED,IAAI,CAAC,KAAK,GAAG,IAAA,qBAAa,EAAC,WAAW,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;QAC/F,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,mBAAmB,iBACtC,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,WAAW,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,IACzC,QAAQ,CAAC,QAAQ,EACpB,CAAA;QACF,IAAI,CAAC,IAAI,GAAG,IAAI,8BAAe,CAAC,IAAI,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,IAAI,EAAE;YAChE,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,MAAM,EAAE,QAAQ,CAAC,EAAE,CAAC,MAAM;YAC1B,KAAK,EAAE,IAAI,CAAC,KAAK;SAClB,CAAC,CAAA;QAEF,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE;YACzB,IAAI,CAAC,oBAAoB,EAAE,CAAA;SAC5B;IACH,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,8BAAe,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;YACjD,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,WAAW,EAAE,IAAI,CAAC,KAAK;SACxB,CAAC,CAAA;IACJ,CAAC;IAED;;OAEG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,0BAAqB,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;IAClF,CAAC;IAUD;;;;OAIG;IACH,IAAI,CAAC,QAAgB;QACnB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IACjC,CAAC;IAED,oEAAoE;IACpE;;;;;;OAMG;IACH,MAAM,CACJ,MAAqB;QAMrB,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAgB,MAAM,CAAC,CAAA;IAChD,CAAC;IAED,iEAAiE;IACjE;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACH,GAAG,CACD,EAAU,EACV,OAAmB,EAAE,EACrB,UAII,EAAE;QAYN,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,EAAE,OAAO,CAAC,CAAA;IACzC,CAAC;IAED;;;;;;OAMG;IACH,OAAO,CAAC,IAAY,EAAE,OAA+B,EAAE,MAAM,EAAE,EAAE,EAAE;QACjE,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IAC1C,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAA;IACpC,CAAC;IAED;;;;;OAKG;IACH,aAAa,CAAC,OAAwB;QACpC,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAA;IAC7C,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,CAAA;IAC1C,CAAC;IAEa,eAAe;;;YAC3B,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,OAAO,MAAM,IAAI,CAAC,WAAW,EAAE,CAAA;aAChC;YAED,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAA;YAE7C,OAAO,MAAA,MAAA,IAAI,CAAC,OAAO,0CAAE,YAAY,mCAAI,IAAI,CAAA;;KAC1C;IAEO,uBAAuB,CAC7B,EACE,gBAAgB,EAChB,cAAc,EACd,kBAAkB,EAClB,OAAO,EACP,UAAU,EACV,QAAQ,EACR,IAAI,EACJ,KAAK,GACqB,EAC5B,OAAgC,EAChC,KAAa;QAEb,MAAM,WAAW,GAAG;YAClB,aAAa,EAAE,UAAU,IAAI,CAAC,WAAW,EAAE;YAC3C,MAAM,EAAE,GAAG,IAAI,CAAC,WAAW,EAAE;SAC9B,CAAA;QACD,OAAO,IAAI,uCAAkB,CAAC;YAC5B,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;YACtB,OAAO,kCAAO,WAAW,GAAK,OAAO,CAAE;YACvC,UAAU,EAAE,UAAU;YACtB,gBAAgB;YAChB,cAAc;YACd,kBAAkB;YAClB,OAAO;YACP,QAAQ;YACR,IAAI;YACJ,KAAK;YACL,KAAK;YACL,wEAAwE;YACxE,gFAAgF;YAChF,4BAA4B,EAAE,eAAe,IAAI,IAAI,CAAC,OAAO;SAC9D,CAAC,CAAA;IACJ,CAAC;IAEO,mBAAmB,CAAC,OAA8B;QACxD,OAAO,IAAI,4BAAc,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,kCAC1C,OAAO,KACV,MAAM,gBAAO,EAAE,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,EAAK,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,KAC7D,CAAA;IACJ,CAAC;IAEO,oBAAoB;QAC1B,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACxD,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,QAAQ,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,YAAY,CAAC,CAAA;QAClE,CAAC,CAAC,CAAA;QACF,OAAO,IAAI,CAAA;IACb,CAAC;IAEO,mBAAmB,CACzB,KAAsB,EACtB,MAA4B,EAC5B,KAAc;QAEd,IACE,CAAC,KAAK,KAAK,iBAAiB,IAAI,KAAK,KAAK,WAAW,CAAC;YACtD,IAAI,CAAC,kBAAkB,KAAK,KAAK,EACjC;YACA,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAA;SAChC;aAAM,IAAI,KAAK,KAAK,YAAY,EAAE;YACjC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAA;YACvB,IAAI,MAAM,IAAI,SAAS;gBAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAA;YAC5C,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAA;SACpC;IACH,CAAC;CACF;AA5TD,iCA4TC"}