#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/tap2go/node_modules/.pnpm/json11@2.0.2/node_modules/json11/dist/node_modules:/mnt/c/Users/<USER>/Desktop/tap2go/node_modules/.pnpm/json11@2.0.2/node_modules/json11/node_modules:/mnt/c/Users/<USER>/Desktop/tap2go/node_modules/.pnpm/json11@2.0.2/node_modules:/mnt/c/Users/<USER>/Desktop/tap2go/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/tap2go/node_modules/.pnpm/json11@2.0.2/node_modules/json11/dist/node_modules:/mnt/c/Users/<USER>/Desktop/tap2go/node_modules/.pnpm/json11@2.0.2/node_modules/json11/node_modules:/mnt/c/Users/<USER>/Desktop/tap2go/node_modules/.pnpm/json11@2.0.2/node_modules:/mnt/c/Users/<USER>/Desktop/tap2go/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../json11@2.0.2/node_modules/json11/dist/cli.mjs" "$@"
else
  exec node  "$basedir/../../../../../../json11@2.0.2/node_modules/json11/dist/cli.mjs" "$@"
fi
