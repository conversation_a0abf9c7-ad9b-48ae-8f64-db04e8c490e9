/*
 * Copyright OpenSearch Contributors
 * SPDX-License-Identifier: Apache-2.0
 *
 * The OpenSearch Contributors require contributions made to
 * this file be licensed under the Apache-2.0 license or a
 * compatible open source license.
 *
 */

/// <reference types="node" />

import {
  AwsSigv4Signer,
  AwsSigv4SignerError,
  AwsSigv4SignerOptions,
  AwsSigv4SignerResponse,
} from './index';

export { AwsSigv4Signer, AwsSigv4SignerOptions, AwsSigv4SignerResponse, AwsSigv4SignerError };
