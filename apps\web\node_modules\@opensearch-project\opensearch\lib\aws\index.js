/*
 * Copyright OpenSearch Contributors
 * SPDX-License-Identifier: Apache-2.0
 *
 * The OpenSearch Contributors require contributions made to
 * this file be licensed under the Apache-2.0 license or a
 * compatible open source license.
 *
 */

'use strict';

const AwsSigv4Signer = require('./AwsSigv4Signer');
const AwsSigv4SignerError = require('./errors');

module.exports = {
  AwsSigv4Signer,
  AwsSigv4SignerError,
};
