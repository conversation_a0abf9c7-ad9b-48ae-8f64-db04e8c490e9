{"name": "@react-email/components", "version": "0.0.25", "description": "A collection of all components React Email.", "sideEffects": false, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist/**"], "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/resend/react-email.git", "directory": "packages/components"}, "keywords": ["react", "email"], "engines": {"node": ">=18.0.0"}, "dependencies": {"@react-email/body": "0.0.10", "@react-email/button": "0.0.17", "@react-email/code-block": "0.0.9", "@react-email/code-inline": "0.0.4", "@react-email/column": "0.0.12", "@react-email/container": "0.0.14", "@react-email/font": "0.0.8", "@react-email/head": "0.0.11", "@react-email/heading": "0.0.14", "@react-email/hr": "0.0.10", "@react-email/html": "0.0.10", "@react-email/img": "0.0.10", "@react-email/link": "0.0.10", "@react-email/markdown": "0.0.12", "@react-email/preview": "0.0.11", "@react-email/render": "1.0.1", "@react-email/row": "0.0.10", "@react-email/section": "0.0.14", "@react-email/tailwind": "0.1.0", "@react-email/text": "0.0.10"}, "peerDependencies": {"react": "^18.0 || ^19.0 || ^19.0.0-rc"}, "devDependencies": {"typescript": "5.1.6", "tsconfig": "0.0.0", "eslint-config-custom": "0.0.0"}, "publishConfig": {"access": "public"}, "scripts": {"build": "tsup src/index.ts --format esm,cjs --dts --external react", "clean": "rm -rf dist", "dev": "tsup src/index.ts --format esm,cjs --dts --external react --watch", "lint": "eslint ."}}