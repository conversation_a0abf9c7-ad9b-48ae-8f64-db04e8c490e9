{"name": "@opensearch-project/opensearch", "description": "The official OpenSearch client for Node.js", "main": "index.js", "types": "index.d.ts", "exports": {".": {"require": "./index.js", "types": "./index.d.ts", "import": "./index.mjs"}, "./aws": "./lib/aws/index.js", "./aws-v3": "./lib/aws/index-v3.js", "./*": "./*"}, "typesVersions": {"*": {".": ["index.d.ts"], "aws": ["./lib/aws/index.d.ts"], "aws-v3": ["./lib/aws/index-v3.d.ts"]}}, "files": ["api/", "lib/", "index.d.ts", "index.js", "index.mjs", "README.md", "LICENSE.txt"], "homepage": "https://www.opensearch.org/", "version": "3.5.1", "versionCanary": "7.10.0-canary.6", "keywords": ["opensearch", "opensearchDashboards", "mapping", "REST", "search", "client", "index"], "scripts": {"test": "npm run lint && tap test/{unit,acceptance}/{*,**/*,**/**/*}.test.js && npm run test:types", "test:unit": "tap test/unit/{*,**/*,**/**/*}.test.js", "test:acceptance": "tap test/acceptance/*.test.js", "test:integration": "node test/integration/index.js", "test:integration:helpers": "tap test/integration/helpers/*.test.js", "test:integration:helpers-secure": "tap test/integration/helpers-secure/*.test.js", "test:types": "tsd", "test:coverage-90": "tap test/{unit,acceptance}/{*,**/*,**/**/*}.test.js --coverage --branches=90 --functions=90 --lines=90 --statements=90 --nyc-arg=\"--exclude=api\"", "test:coverage-report": "tap test/{unit,acceptance}/{*,**/*,**/**/*}.test.js --coverage --branches=90 --functions=90 --lines=90 --statements=90 --nyc-arg=\"--exclude=api\" && nyc report --reporter=text-lcov > coverage.lcov", "test:coverage-ui": "tap test/{unit,acceptance}/{*,**/*,**/**/*}.test.js --coverage --coverage-report=html --nyc-arg=\"--exclude=api\"", "lint": "eslint .", "lint:fix": "eslint . --fix", "license-checker": "license-checker --production --onlyAllow='MIT;Apache-2.0;Apache1.1;0BSD;ISC;BSD-3-Clause;BSD-2-Clause'", "build-esm": "npx gen-esm-wrapper . index.mjs && eslint --fix index.mjs"}, "author": "opensearch-project", "original-author": {"name": "<PERSON>", "company": "Elasticsearch BV"}, "devDependencies": {"@aws-sdk/types": "^3.160.0", "@babel/eslint-parser": "^7.19.1", "@sinonjs/fake-timers": "github:sinonjs/fake-timers#0bfffc1", "@types/node": "^22.0.0", "convert-hrtime": "^5.0.0", "cross-zip": "^4.0.0", "dedent": "^1.1.0", "deepmerge": "^4.2.2", "dezalgo": "^1.0.3", "eslint": "^8.30.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "faker": "^5.5.3", "fast-deep-equal": "^3.1.3", "into-stream": "^6.0.0", "js-yaml": "^4.1.0", "jsdoc": "^4.0.0", "license-checker": "^25.0.1", "minimist": "^1.2.5", "node-fetch": "^3.2.10", "ora": "^8.0.1", "prettier": "^3.0.1", "pretty-hrtime": "^1.0.3", "proxy": "^1.0.2", "rimraf": "^6.0.1", "semver": "^7.3.5", "simple-git": "^3.15.0", "simple-statistics": "^7.7.0", "split2": "^4.1.0", "stoppable": "^1.1.0", "tap": "^16.3.0", "tsd": "^0.27.0", "workq": "^3.0.0", "xmlbuilder2": "^3.0.2"}, "dependencies": {"aws4": "^1.11.0", "debug": "^4.3.1", "hpagent": "^1.2.0", "json11": "^2.0.0", "ms": "^2.1.3", "secure-json-parse": "^2.4.0"}, "resolutions": {"**/strip-ansi": "^6.0.1"}, "license": "Apache-2.0", "repository": {"type": "git", "url": "https://github.com/opensearch-project/opensearch-js.git"}, "bugs": {"url": "https://github.com/opensearch-project/opensearch-js/issues"}, "engines": {"node": ">=14", "yarn": "^1.22.10"}, "tsd": {"directory": "test/types"}, "tap": {"ts": false, "jsx": false, "flow": false, "coverage": false, "jobs-auto": true}}