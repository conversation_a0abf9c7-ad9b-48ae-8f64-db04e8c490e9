/*
 * Copyright OpenSearch Contributors
 * SPDX-License-Identifier: Apache-2.0
 *
 * The OpenSearch Contributors require contributions made to
 * this file be licensed under the Apache-2.0 license or a
 * compatible open source license.
 *
 */

/*
 * This file was generated from the OpenSearch API Spec. Do NOT edit it
 * manually. If you want to make changes, either update the spec or
 * modify the API generator.
 */

'use strict';

/** @namespace API-Wlm */

function WlmApi(bindObj) {
  this.createQueryGroup = require('./createQueryGroup').bind(bindObj);
  this.deleteQueryGroup = require('./deleteQueryGroup').bind(bindObj);
  this.getQueryGroup = require('./getQueryGroup').bind(bindObj);
  this.updateQueryGroup = require('./updateQueryGroup').bind(bindObj);

  // Deprecated: Use createQueryGroup instead.
  this.create_query_group = require('./createQueryGroup').bind(bindObj);
  // Deprecated: Use deleteQueryGroup instead.
  this.delete_query_group = require('./deleteQueryGroup').bind(bindObj);
  // Deprecated: Use getQueryGroup instead.
  this.get_query_group = require('./getQueryGroup').bind(bindObj);
  // Deprecated: Use updateQueryGroup instead.
  this.update_query_group = require('./updateQueryGroup').bind(bindObj);
}

module.exports = WlmApi;
